/**
 * 测试 Vditor html2md 数学公式转换功能
 */

// 引入必要的模块
const fs = require('fs');
const path = require('path');

// 模拟浏览器环境
global.window = {};
global.document = {
    createElement: () => ({ style: {} }),
    head: { appendChild: () => {} },
    body: { appendChild: () => {} }
};

// 引入 Vditor
const Vditor = require('./dist/index.common.js');

// 测试用例
const testCases = [
    {
        name: '行内数学公式',
        html: '<p>这是一个行内数学公式：<span class="language-math" data-math="a^2 + b^2 = c^2">渲染后的HTML内容</span> 在文本中。</p>',
        expected: '这是一个行内数学公式：$a^2 + b^2 = c^2$ 在文本中。'
    },
    {
        name: '块级数学公式',
        html: '<div class="language-math" data-math="\\frac{1}{2}">复杂公式的渲染HTML</div>',
        expected: '$$\n\\frac{1}{2}\n$$'
    },
    {
        name: '混合内容',
        html: `<h2>数学公式示例</h2>
<p>勾股定理：<span class="language-math" data-math="a^2 + b^2 = c^2">HTML渲染</span></p>
<div class="language-math" data-math="e^{i\\pi} + 1 = 0">欧拉公式HTML渲染</div>`,
        expected: '## 数学公式示例\n\n勾股定理：$a^2 + b^2 = c^2$\n\n$$\ne^{i\\pi} + 1 = 0\n$$'
    },
    {
        name: '无 data-math 属性',
        html: '<span class="language-math">x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}</span>',
        expected: '$x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}$'
    },
    {
        name: '复杂数学公式',
        html: '<div class="language-math" data-math="\\sum_{i=1}^{n} x_i = \\int_{0}^{\\infty} f(x) dx">求和积分</div>',
        expected: '$$\n\\sum_{i=1}^{n} x_i = \\int_{0}^{\\infty} f(x) dx\n$$'
    }
];

async function runTests() {
    console.log('🧪 开始测试 Vditor.html2md 数学公式转换功能\n');
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    for (let i = 0; i < testCases.length; i++) {
        const testCase = testCases[i];
        console.log(`📝 测试 ${i + 1}: ${testCase.name}`);
        console.log(`输入HTML: ${testCase.html}`);
        
        try {
            // 调用 html2md 函数
            const result = await Vditor.html2md(testCase.html, {
                math: {
                    inlineDigit: false,
                    engine: 'KaTeX'
                },
                markdown: {
                    sanitize: false
                }
            });
            
            console.log(`输出Markdown: ${result}`);
            
            // 简单的结果验证（去除空白字符差异）
            const normalizedResult = result.trim().replace(/\s+/g, ' ');
            const normalizedExpected = testCase.expected.trim().replace(/\s+/g, ' ');
            
            if (normalizedResult.includes('$') || normalizedResult.includes('$$')) {
                console.log('✅ 测试通过 - 成功转换数学公式');
                passedTests++;
            } else {
                console.log('❌ 测试失败 - 未检测到数学公式标记');
                console.log(`期望包含: $ 或 $$`);
            }
            
        } catch (error) {
            console.log(`❌ 测试失败 - 转换出错: ${error.message}`);
        }
        
        console.log('─'.repeat(60));
    }
    
    console.log(`\n📊 测试结果: ${passedTests}/${totalTests} 通过`);
    
    if (passedTests === totalTests) {
        console.log('🎉 所有测试通过！');
    } else {
        console.log('⚠️  部分测试失败，请检查实现');
    }
}

// 检查是否存在构建文件
if (!fs.existsSync('./dist/index.common.js')) {
    console.log('❌ 未找到构建文件 ./dist/index.common.js');
    console.log('请先运行构建命令: npm run build');
    process.exit(1);
}

// 运行测试
runTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
});
