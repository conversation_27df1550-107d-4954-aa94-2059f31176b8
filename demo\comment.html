<!DOCTYPE html>
<html lang="zh-cmn-Hans">
<head>
    <meta charset="utf-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0"/>
    <meta name="theme-color" content="#f1f7fe">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes"/>
    <meta name="apple-mobile-web-app-status-bar-style" content="black">
    <title>Vditor: ♏ An In-browser Markdown editor, support WYSIWYG, Instant Rendering (Typora-like) and Split View
        modes. 一款浏览器端的 Markdown 编辑器，支持所见即所得、即时渲染（类似 Typora）和分屏预览模式。</title>
    <meta name="description"
          content="Vditor 支持三种所见即所得（wysiwyg）、即时渲染（ir）、分屏预览（sv）模式，支持大纲、数学公式、脑图、图表、流程图、甘特图、时序图、五线谱、多媒体、语音阅读、标题锚点、代码高亮及复制、graphviz 渲染。"/>
    <meta property="og:description"
          content="Vditor 支持三种所见即所得（wysiwyg）、即时渲染（ir）、分屏预览（sv）模式，支持大纲、数学公式、脑图、图表、流程图、甘特图、时序图、五线谱、多媒体、语音阅读、标题锚点、代码高亮及复制、graphviz 渲染。"/>
    <meta name="twitter:description" property="og:description" itemprop="description"
          content="Vditor 支持三种所见即所得（wysiwyg）、即时渲染（ir）、分屏预览（sv）模式，支持大纲、数学公式、脑图、图表、流程图、甘特图、时序图、五线谱、多媒体、语音阅读、标题锚点、代码高亮及复制、graphviz 渲染。"/>
    <link rel="dns-prefetch" href="//cdn.jsdelivr.net/"/>
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="icon" type="image/png" href="https://cdn.jsdelivr.net/npm/vditor/dist/images/logo.png"/>
    <link rel="apple-touch-icon" href="https://cdn.jsdelivr.net/npm/vditor/dist/images/logo.png">
    <link rel="shortcut icon" type="image/x-icon" href="https://cdn.jsdelivr.net/npm/vditor/dist/images/logo.png">
    <meta name="copyright" content="B3log"/>
    <meta http-equiv="Window-target" content="_top"/>
    <meta property="og:locale" content="zh-cmn-Hans"/>
    <meta property="og:title"
          content="Vditor: ♏ An In-browser Markdown editor, support WYSIWYG,  Instant Rendering (Typora-like) and Split View modes. 一款浏览器端的 Markdown 编辑器，支持所见即所得、即时渲染（类似 Typora）和分屏预览模式。"/>
    <meta property="og:site_name" content="Blog-vditor"/>
    <meta property="og:url" content="https://b3log.org/vditor"/>
    <meta name="twitter:card" content="summary"/>
    <meta name="twitter:domain" content="b3log.org"/>
    <meta name="twitter:title" property="og:title" itemprop="b3log vditor"
          content="Vditor: ♏ An In-browser Markdown editor, support WYSIWYG,  Instant Rendering (Typora-like) and Split View modes. 一款浏览器端的 Markdown 编辑器，支持所见即所得、即时渲染（类似 Typora）和分屏预览模式。"/>
    <meta name="twitter:site" content="@B3logOS"/>
    <meta name="twitter:url" content="https://b3log.org/vditor"/>
    <meta property="og:image" content="https://cdn.jsdelivr.net/npm/vditor/dist/images/logo.png"/>
    <meta name="twitter:image" content="https://cdn.jsdelivr.net/npm/vditor/dist/images/logo.png"/>
    <style>
        body {
            margin: 0;
        }

        .nav {
            text-align: center;
            margin: 20px 0
        }

        a {
            color: #4285f4;
        }

        #vditor {
            margin: 0 20px 0 20px;
        }

        #comments > div {
            position: absolute;
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/vconsole@3.3.4/dist/vconsole.min.js"></script>
</head>
<body>
<div class="vditor-reset nav">
    <a href="https://b3log.org/vditor" target="_blank">官网</a> &nbsp; &nbsp;
    <a href="/render.html">Render</a> &nbsp; &nbsp;
    <button onclick="window.vditor.setTheme('dark', 'dark',  'native');document.querySelector('body').style.backgroundColor='#2f363d'">
        Dark Mode
    </button> &nbsp; &nbsp;
    <button onclick="window.vditor.setTheme('light', 'light', 'github');document.querySelector('body').style.backgroundColor=''">
        Light Mode
    </button>
</div>
<div style="display: flex;height: 360px">
    <div id="vditor" style="flex: 1"></div>
    <div id="comments" style="width: 300px;overflow: auto;margin-top: 36px;position: relative"></div>
</div>
</body>
</html>
