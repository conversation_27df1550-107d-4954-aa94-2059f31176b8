
FPU数字后端实现教程
本教程是基于90NM工艺的FPU浮点计算单元的数字后端实现，涉及逻辑综合，布局布线等方面。
一、逻辑综合
对于高频模块或者绕线不太好绕的模块，都建议采用DCT做综合。一方面可以改善timing，另外一方面可以节省面积。
优化选项的设置读者可通过man自行进行研究。下面介绍DCT的主要流程：
1)首先我们在dct_setup.tcl中定义了工艺库的文件的位置，设计开始之前需要先读取该文件。值得注意的是工艺库文件所在的位置其实是在common_setup.tcl里面。在公司的实际flow中，往往会嵌套更多层，主要原因是因为某些参数要重复使用，因此往往将变量定义在统一的位置（避免出现一个变量多次定义冲突问题！）。

备注：dct启动方式：dc_shell -topo

2)然后我们需要建立设计的物理参考库，通过create_mw_lib创建mw库，然后通过open_mw_lib打开所创建的物理库。


这里，需要指出的是上面的脚本中有个extend_mw_layers的语句。当设计中的layer number超出255的时候，在创建library的时候必须添加这个命令才能成功创建。一般在先进工艺中，比如14nm，都需要打开这个选项。
3)在完成上述设置之后，我们需要设置tluplus文件。该文件为	RC参数抽取模型文件，用于计算线延迟，之后我们需要check_tlu_plus_files，出现三个“pass”即可认为文件设置正常。这步如何check不通过，后续的任何优化都是徒劳的，因为没有RC信息。
4)然后我们通过analyze和elaborate这两条命令读入rtl代码，使用link检查代码和工艺库直接的映射是否正常。
5)之后source时序约束文件，在时序约束文件中我们需要定义系统的时钟周期、setup和hold的uncertainty值、输入输出端口的延迟时间、最大转换时间、最大电容和最大扇出等约束。
6)完成上述设置之后，我们就可以设置group_path，然后使用compile_ultra进行综合，在综合完成之后，可以使用report_timing和report_constraint等命令检查时序约束。
7)在本设计中，由于sram路径延时较大，而相邻路径又存在较大的余量，所以通过set_clock_latency向后借时序。读者有兴趣可以进行深入研究。
8)在时序约束调整之后，可以用compile_ultra -incr命令进行增量优化
9)优化完如果时序满足要求，即可输出网表和sdc时序约束文件进行后端设计。
具体的脚本可以参考虚拟机上 syn/scripts/rm_dc_scripts/dc.tcl

二、设计导入
1.首先是创建一个milkyway library，这个与DCT综合阶段创建的library是一样的。这部分内容也可以参考公众号上的design import文章。

create_mw_lib \
     -tech $TECH_FILE \
     -bus_naming_style {[%d]} \
     -mw_reference_library $MW_REFERENCE_LIB_DIRS \
     $MW_DESIGN_LIBRARY
open_mw_lib $MW_DESIGN_LIBRARY

#Set the RC parameter extraction model
set_tlu_plus_files -max_tluplus $TLUPLUS_CMAX -min_tluplus $TLUPLUS_CMIN -tech2itf_map $TLUPLUS_MAP
check_tlu_plus_files

#import design
import_designs $ICC_IN_VERILOG_NETLIST_FILE -format verilog -top $DESIGN_NAME -cel $DESIGN_NAME
current_design $DESIGN_NAME
uniquify_fp_mw_cel
##check whether any unsolved issue(important)
link -force

典型的错误1：

出现上面这个错误的时候要根据log提示找线索。从log上看到，工具提示FADDX1这个cell没有定义，而这个明显是标准单元，这说明创建milkyway library时缺少这套库。那么怎么去做检查是否真的少了呢？因为当你的参考库有很多的时候，往往是以list的形式写的，list中每行的换行符多一个空格都不可以。所以可以下面的命令来检查是否有某些参考库没有成功加入。
report_mw_lib -mw_reference_library

典型错误2:



这个就比较简单了，很显然是top module名字写错了。

典型警告3：



这种警告是必须处理的，否则timing是不准的。出现un resolved是因为SRAM缺lib（db）。值得注意的是，任何一套参考库的milkyway和lib的端口必须完全一致，否则也会出现verilog读入错误和un resolved的问题。


三、Floorplan&Powerplan
Floorplan主要分为几块，分别是floorplan定义、memory摆放、pin的摆放、tapcell和endcap单元插入、电源网格的规划等等。
Floorplan定义
Floorplan大小，第一次不熟悉的同学可以直接指定长度和宽度，本案例先使用820um*820um。Floorpan大小的指定往往有三种模式，具体见下图。




但实际项目中block的形状大多都不是方方正正的，更多的是N多边型的。而block的形状是由top根据block前期评估的面积数据来做partition的。

思考题：keep住当前模块的面积，如何读入一个多边形的形状？如何画一个特定面积的多边形呢？

Memory摆放
主要涉及Memory如何摆放，Memory 之间间距如何预留。宏单元尽量沿着四边摆放，中间摆放标准单元，同时可以根据数据流进行摆放。
Pin assignment
对于模块的pin，摆放原则需要满足以下几点：
时钟信号摆放在出pin位置中间部分。
同一组bus摆放在一起，最好是按照位宽顺序来摆放。

本案例要求各位将所有的port摆放在某一侧。摆放的脚本在知识星球上有。

Tapcell and endcap insertion
这部分内容参加微信公众号推送的历史文章，比较简单。
PowerNetwork planning
本设计不需要做power domain，因此只存在两条power net的设计规划，分别是VDD，VSS 的设计。设计的原则是考虑IR drop，EM，以及power设计对route资源的影响。
电源网格流程分别如下：创建宏单元的power ring，定义整体的power stripe，连接宏单元（Memory/IP）的power net，创建标准单元的power rail。

PS: 在绕线资源充足的条件下，power的density要尽量密集。因为IR drop大了，很容易影响设计的性能甚至影响正常功能。

Verify_pg检查
做完floorplan&powerplan后，务必要进行verify_pg_nets来检查是否有floating的pin，floating的net，比如channel的cell没有供电网络，缺孔少孔等现象。



上面这两个图的结果其实都是没问题的。前者是verify_pg之前已经将所有的标准单元全部摆放到core区域了。而后者则没有，所以会出现标准单元的VDD和VSS floating的情况。

那么，verify_pg后怎么查看结果呢？

通过GUI界面 Verification----> Error Browser，进入Error View界面，选择Rail选项，选择verify后的cell























四、Place阶段

Place阶段务必要把所有的memory fix住，这步我们在floorpan就已经做了。否则工具会把memory当作标准单元一起做placement，这个是我们所不想看到的结果。

Place阶段还需要检查所有的clock是否已经设置ideal network。因为place阶段我们不希望工具去解clock net的fanout。

Log检查

很多初学者往往仅仅是停留在run flow，等着看timing report的状态。其实这是非常不好的一个习惯。可能你的领导会问实现的overflow是多少，clock skew是多少，clock tree的级数是多少，那如果你不用心去做项目，你可能都回答不上。这时候老板就知道你是一个不善于学习的同学。因为做后端PR一定要看log，log中可能有出乎意料的fatal error,这种错误可能是致命的，直接导致你的评估结果完全不准，比如某些时钟约束没设上。当然log中还有很多关于工具行为的信息，比如placement到底都在做哪些步骤等等。

Max cell displacement

无论是在跑flow还是在timing fixing阶段，都需要关注max cell displacement信息。我们知道place过程是先做corase placement，然后再调用legalize engine来将标准单元摆放在row上，最后再做area,power,timing的进一步优化。

如果出现非常大的max cell displacement，说明工具在做legalize的时候与corase placment的情景有所不一样，此时大概率是会出现timing变差的情况。当然如果设计本身timing就比较松，可能影响看不出来。越是高频的设计，这种影响就越明显。

在timing fixing阶段，如果出现很大的max cell displacement，就会出现PT中看到的timing和ICC eco route后timing不match的情况。这种情况会导致整个timing signoff需要更多轮的迭代次数，甚至无法收敛timing。


Overflow查看

如果设计的overflow特别大，经常会卡在某个phase，而且一直出不来。当你发现flow跑的特别慢的时候，要去看看log中间过程的overflow是否有异常。

这个report信息列出来每一层的overflow情况，包括总的overflow和最大overflow是多少，已经有多少GRC存在overflow。需要注意的是，这个overflow值是工具预估出来的，而且有overflow也并不意味后续绕线就一定有问题。

本设计的overflow信息如下图所示。很明显一定是可以绕得通的，因为总的overflow数量少，而且max值还不大。如果你想增加难度，建议将block的面积进一步缩小，比如将利用率提升至85%再去try下flow，看看这个overflow的变化。



WNS&TNS


从log上可以看到工具在做时序优化过程的各种信息，比如面积Area,WNS和TNS等等。有的时候模块run time会特别久，一直等跑完place可能需要十几个小时，那么如果你学会分析中间过程，可以提早发现一些问题，并行改一版或者多个版本，再去try flow。其实这就是并行流水线pipeline的思想。

如果log上看到WNS特别大，如上图所示WNS为2.39ns，WNS并没有特别大，这说明存在violation的endpoint数量不是特别多。有的时候wns为1ns,TNS可能也才30ns，那这种情况大概率问题不大。而且右侧的ENDPOINT信息有的时候也能给你点提示，比如output port有很大的timing violation，往往是output delay约束可能有问题导致的。

Timing optimization
placement主要的工作是标准单元的摆放，这一步相对简单，不做过多介绍，但需要多多注意时序违例，在CTS之前都只关系setup违例。


这条path的violation是多少呢？Slack=5.04-7.43=-2.39ns。从report上看到时钟T=5.8ns，而sram的CE-->Q的delay就有7.08ns，所以这条path天然是无法meet的。

那怎么办呢？不用怕，我们可以看看前后级的timing是否有足够的margin。经分析后一级有足够的timing maring，所以我们可以将这颗sarm的latency“提前”一些，比如通过set_clock_latency设置一个-2.5ns。

经过优化后place timing完全meet。

完成placement之后，我们需要插入tie cell，将接“1”和接”0“的单元给接到VDD和VSS上。当然有的flow会把这步挪到Post-CTS阶段，这个没有本质区别。

细分Group path
为了防止IO接口，即input和output相关path的timing影响到internal timing，我们默认都需要将它们单独group出来。默认需要拎出来的group path有以下几种：

Input---> output (feedthrough path)
Input----> reg
Reg----> output

  group_path -name REGOUT -to [all_outputs]
  group_path -name REGIN -from [remove_from_collection [all_inputs] $ports_clock_root]
  group_path -name FEEDTHROUGH -from [remove_from_collection [all_inputs] $ports_clock_root] -to [all_outputs]

为了最大限度改善timing，需要细分group_path。将design中各个子模块拎出来，分别设置group path。并根据每个group path的timing，调整weight值，从而加大力度优化critical path的时序。
ARM的官方推荐了根据timing自动调整weight的脚本，有需要的同学可以前往知识星球下载。
Clock gating timing预处理
防止CTS后，到clock gating enable pin setup比较差的情况，在place前需要将clock gating的clock latency提前（设置负的clock latency值）。同时加大maring，让工具最大程度上优化datapath，使得datapath最短。
Leakage optimization
为了降低leakage，在place阶段采用中阈值的Library作为optimization的target library。
Placement quality checkPlace
做完一定 要查看以下几个report
Timing report（setup的wns，tns）
Timing DRC report（max_transition report）
Congestion map
Cell density map





五、Clock tree synthesis/Timing optimization
时钟树综合的两大目标即clock skew最小，clock latency最短。
CTO的目标是timing满足设计要求，congestion map和celldensity合理。
Clock skew最小
clock skew
这里所说的clock skew最小是指local clock skew最小。关于global skew和local skew的概念，请参见公众号推送的文章。
由于本次设计的时钟结构简单，其中只有一个时钟core_clk。因此本设计主要想办法做到如下几点：
Clock gating所带的寄存器等sinks，他们之间的uncommon clock path要最短。实现的方法有如下两种方法：
1.综合阶段clock gating的fanout设置相对小点。
2.Place 阶段将clock gating所带的寄存器，聚拢在一起。实现方法用set_net weight。。
Clock latency最短
Core_clk所带的sinks，不外乎是寄存器和memory。考虑到TO_MEMORY可能存在的hold violation，前期需要将memory的clock tree尽量做短（hold requirement比较大）。同时，需要注意的是在将memory最短时，设置的floating pin值需要合适，否则会适得其反，反而拖长整个clock tree的长度。
注意：并不是说Timing和Physical都没问题，就一定是OK的。Clock tree的长度和clockskew需要重点考虑。
Clock tree上clock inverter的选取
这点请参考公众号推送的文章。需要说明的一点是，时钟树上建议选用LVT（clock tree质量最好）。
clock inverter选型
NDR Rule设置
对于高频模块，本设计对clock net，采取三倍宽度，三倍间距的non-default route rule。普通模块的ndr可以只设定2倍width，2倍space。这部分的设置，微信公众号也推送过。
时钟树综合实践篇
Timing DRC满足要求
这里主要是指data和clock的max transition，TIE-HI 和TIE-LOW的max capacitance以及max_fanout等Timing DRC的约束。
通常情况下，对于高频模块，其clock transition要比普通模块设置的更小即更为严格。普通模块一般设置为时钟周期的20%，高频模块则设置为时钟周期的10%-15%。关于max clock/data transition的约束标准/依据，可以查看知识星球相关主题。

CCD的应用
为了最大限度利用useful skew，我们可以利用工具的CCD功能，实现更高的频率。在没有这个功能的时候，我们需要自己去调useful skew。有的时候我们可能都会抓出上千个reg，设置floating值。
手动调整时钟树
如上所述，我们可以将综合阶段设置了latency的sink点，需要设置floating pin来手动修改时钟树，这部分修改是本项目案例的精髓，读者可以自行探索。latency值的大小应该如何调整才能实现时钟树的最优化。单纯通过CCD来调useful skew将会失去后端中最重要技能的培养，无法理解时钟树综合的精髓。


CTS后不得不关注的那些信息：

Clock Skew Report

无论是通过查看log还是通过report_clock_tree来报出下图所示的clock tree信息。从中可以看到，clock name，scenario, 周期，时钟树级数，sink数量，cts clock buffer，以及时钟最长路径和最短路径。记住，长tree与时钟周期没有任何关系。


有了上面那些信息后，我们就能知道整个clock tree做的质量了。但是，如果你发现时钟树质量不好，想要进一步分析，那些信息又不太够。那下面就介绍一个CTS分析利器。那就是你可以将时钟树的所有级数展开，可以通过下面命令来实现。通过这个report，我们就可以很清晰看到工具是如何一级级长tree，每个tree的去向也一清二楚。

当然，对于比较复杂时钟结构的设计，还是需要提前分析时钟结构，画出时钟树结构图。这样当你看这个report的时候会非常有感觉，有种你一边看report一边review工具长tree的情况。一旦发现有个不该穿过的cell，立马就知道问题所在。这个能力需要大家多积累工作经验，多练习，这是一个循序渐进的过程。

report_clock_tree -structure -show_all_sinks



当你看到某段tree上有一大堆的CTO_delay时，第一反应就是要怀疑这部分tree是被别人拖长的。这个类似于Innovus中的*cdb关键词。知道了这个之后就可以继续debug到底是在哪个分支上出问题。





六、Route阶段
Timing optimization
在route优化timing阶段，tool仍然可以利用CCD engine来微调clock tree来优化timing和power。除了上面介绍的cto阶段需要给IO接口设置的skip_path外，我们还需要额外添加某些group。这些path group在cto阶段timing是比较好的，而route后发现timing degrate特别多，经过debug发现问题出在clock tree上，对应launch clock path上较cto后多垫了两对clock inverter.
至于工具为何有这样的behavior，留给各位思考。
将route阶段本不应该考虑CCD的path group添加到skip_path_group 中后，整个route的QOR就变得比较好了。
Long net处理
对于高频cpu的design，我们对于long net，还需要做特殊处理。因为芯片很多时候是应用于消费类电子，通常都是拼跑分，拼性能（即使实际情况都是性能过剩）。所以我们需要对design进行overdrive，即会抬高电压，获得更好的性能。
在本次设计实现中，我们可以将net长度大于200的，都进行split net操作，从而避免long net引起的transition问题。
具体快速方法：.
1.写脚本将net_length大于200的net，全部抓出来
2.利用add_buffer on_route命令将这样的net，一分为二。（这个命令也是修transition的万能命令，强烈推荐使用！）

Short处理
通常情况绕线后可能会有一些short。小编知识星球上有一个非常高效，自动修复short的脚本，感兴趣的可以移步星球查阅。

Short修复除了用小编提供的脚本外，还可以通过以下几种方式来修复。

1.挪cell使得local pin density降低，从而缓解绕线紧张的情况；
2.添加route blockage;
3.设置keepout margin/cell padding
4.降低block利用率/density
5.更改route迭代次数

天线效应
为了防止出现天线效应，我们需要提前读入工艺库提供的脚本，在布线前完成设置。


七、Finish阶段
decap cell插入
为了避免IR drop，减小动态功耗，建议插入decap cell。特别是对于某些module翻转率比较高的情况，还需要预先插decap cell来改善动态IR Drop。

filler cell插入

为了将版图中空白的部分填满，把扩散层连接起来满足DRC规则和设计需求，在最后我们需要插入filler cell。

在实际项目中，我们除了插带decap的filler和普通filler外，还会插另外一种CELL。这种cell就是通常所说的ECO cell或者Spare cell。这种CELL是为了做ECO用的。




