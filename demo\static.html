<!doctype html>
<html lang="zh-cmn-Hans">
    <head>
        <meta charset="utf-8" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta
            name="viewport"
            content="width=device-width, initial-scale=1.0, maximum-scale=1.0"
        />
        <meta name="theme-color" content="#f1f7fe" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="black" />
        <title>
            Vditor: ♏ An In-browser Markdown editor, support WYSIWYG, Instant
            Rendering (Typora-like) and Split View modes. 一款浏览器端的
            Markdown 编辑器，支持所见即所得、即时渲染（类似
            Typora）和分屏预览模式。
        </title>
        <meta
            name="description"
            content="Vditor 支持三种所见即所得（wysiwyg）、即时渲染（ir）、分屏预览（sv）模式，支持大纲、数学公式、脑图、图表、流程图、甘特图、时序图、五线谱、多媒体、语音阅读、标题锚点、代码高亮及复制、graphviz 渲染。"
        />
        <meta
            property="og:description"
            content="Vditor 支持三种所见即所得（wysiwyg）、即时渲染（ir）、分屏预览（sv）模式，支持大纲、数学公式、脑图、图表、流程图、甘特图、时序图、五线谱、多媒体、语音阅读、标题锚点、代码高亮及复制、graphviz 渲染。"
        />
        <meta
            name="twitter:description"
            property="og:description"
            itemprop="description"
            content="Vditor 支持三种所见即所得（wysiwyg）、即时渲染（ir）、分屏预览（sv）模式，支持大纲、数学公式、脑图、图表、流程图、甘特图、时序图、五线谱、多媒体、语音阅读、标题锚点、代码高亮及复制、graphviz 渲染。"
        />
        <link rel="dns-prefetch" href="//cdn.jsdelivr.net/" />
        <link rel="preconnect" href="https://cdn.jsdelivr.net" />
        <link
            rel="icon"
            type="image/png"
            href="https://cdn.jsdelivr.net/npm/vditor/dist/images/logo.png"
        />
        <link
            rel="apple-touch-icon"
            href="https://cdn.jsdelivr.net/npm/vditor/dist/images/logo.png"
        />
        <link
            rel="shortcut icon"
            type="image/x-icon"
            href="https://cdn.jsdelivr.net/npm/vditor/dist/images/logo.png"
        />
        <meta name="copyright" content="B3log" />
        <meta http-equiv="Window-target" content="_top" />
        <meta property="og:locale" content="zh-cmn-Hans" />
        <meta
            property="og:title"
            content="Vditor: ♏ An In-browser Markdown editor, support WYSIWYG,  Instant Rendering (Typora-like) and Split View modes. 一款浏览器端的 Markdown 编辑器，支持所见即所得、即时渲染（类似 Typora）和分屏预览模式。"
        />
        <meta property="og:site_name" content="Blog-vditor" />
        <meta property="og:url" content="https://b3log.org/vditor" />
        <meta name="twitter:card" content="summary" />
        <meta name="twitter:domain" content="b3log.org" />
        <meta
            name="twitter:title"
            property="og:title"
            itemprop="b3log vditor"
            content="Vditor: ♏ An In-browser Markdown editor, support WYSIWYG,  Instant Rendering (Typora-like) and Split View modes. 一款浏览器端的 Markdown 编辑器，支持所见即所得、即时渲染（类似 Typora）和分屏预览模式。"
        />
        <meta name="twitter:site" content="@B3logOS" />
        <meta name="twitter:url" content="https://b3log.org/vditor" />
        <meta
            property="og:image"
            content="https://cdn.jsdelivr.net/npm/vditor/dist/images/logo.png"
        />
        <meta
            name="twitter:image"
            content="https://cdn.jsdelivr.net/npm/vditor/dist/images/logo.png"
        />
        <style>
            body {
                margin: 0;
                padding: 0 20px;
                font-family: -apple-system, BlinkMacSystemFont, "Segoe UI",
                    Roboto, sans-serif;
            }

            .nav {
                text-align: center;
                margin: 20px 0;
            }

            a {
                color: #4285f4;
            }

            .demo-section {
                margin: 30px 0;
                padding: 20px;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                background: #f9f9f9;
            }

            .demo-section h2 {
                margin-top: 0;
                color: #333;
                border-bottom: 2px solid #4285f4;
                padding-bottom: 10px;
            }

            .demo-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 20px;
                margin: 20px 0;
            }

            .demo-panel {
                background: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 15px;
            }

            .demo-panel h3 {
                margin-top: 0;
                color: #666;
                font-size: 16px;
            }

            textarea {
                width: 100%;
                height: 200px;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 10px;
                font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
                font-size: 14px;
                resize: vertical;
                box-sizing: border-box;
            }

            .demo-buttons {
                text-align: center;
                margin: 15px 0;
            }

            button {
                background: #4285f4;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
                margin: 0 5px;
                transition: background-color 0.3s;
            }

            button:hover {
                background: #3367d6;
            }

            button:disabled {
                background: #ccc;
                cursor: not-allowed;
            }

            .status {
                padding: 10px;
                border-radius: 4px;
                margin: 10px 0;
                text-align: center;
                font-size: 14px;
            }

            .status.success {
                background: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }

            .status.error {
                background: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }

            .status.loading {
                background: #d1ecf1;
                color: #0c5460;
                border: 1px solid #bee5eb;
            }

            .examples {
                margin: 20px 0;
            }

            .example-item {
                margin: 10px 0;
                padding: 10px;
                background: white;
                border: 1px solid #ddd;
                border-radius: 4px;
            }

            .example-item h4 {
                margin: 0 0 10px 0;
                color: #555;
                font-size: 14px;
            }

            .example-html {
                background: #f5f5f5;
                padding: 8px;
                border-radius: 3px;
                font-family: monospace;
                font-size: 12px;
                margin-bottom: 8px;
                white-space: pre-wrap;
                word-break: break-all;
            }

            .example-item button {
                padding: 5px 10px;
                font-size: 12px;
            }

            @media (max-width: 768px) {
                .demo-grid {
                    grid-template-columns: 1fr;
                }

                body {
                    padding: 0 10px;
                }
            }
        </style>
        <link
            rel="stylesheet"
            href="https://cdn.jsdelivr.net/npm/vditor@3.10.7/dist/index.css"
        />
        <script src="https://cdn.jsdelivr.net/npm/vditor@3.10.7/dist/index.min.js"></script>
    </head>

    <body>
        <div class="vditor-reset nav">
            <a href="https://b3log.org/vditor" target="_blank">官网</a>
        </div>
        <div id="vditor">
            <h1>Vditor</h1>
            <ul>
                <li>foo</li>
                <li>bar</li>
            </ul>
        </div>

        <div class="demo-section">
            <div class="demo-grid">
                <div class="demo-panel">
                    <h3>HTML 输入</h3>
                    <textarea
                        id="htmlInput"
                        placeholder="在这里输入 HTML 代码..."
                    >
<h1>Vditor 编辑器</h1>
<p>这是一个<strong>强大</strong>的 Markdown 编辑器，支持：</p>
<ul>
    <li><em>所见即所得</em>编辑</li>
    <li><a href="https://github.com/Vanessa219/vditor">开源项目</a></li>
    <li><code>代码高亮</code></li>
</ul>
<blockquote>
    <p>为未来而构建的下一代 Markdown 编辑器</p>
</blockquote>
<h2>代码示例</h2>
<pre><code class="language-javascript">// 初始化编辑器
const vditor = new Vditor('vditor', {
    mode: 'wysiwyg',

});
</code></pre>
      </textarea
                    >
                </div>

                <div class="demo-panel">
                    <h3>Markdown 输出</h3>
                    <textarea
                        id="markdownOutput"
                        readonly
                        placeholder="转换后的 Markdown 将显示在这里..."
                    ></textarea>
                </div>
            </div>

            <div id="status"></div>
        </div>

        <script>
            // 获取DOM元素
            const htmlInput = document.getElementById("htmlInput");
            const markdownOutput = document.getElementById("markdownOutput");
            const convertBtn = document.getElementById("convertBtn");
            const clearBtn = document.getElementById("clearBtn");
            const copyBtn = document.getElementById("copyBtn");
            const roundTripBtn = document.getElementById("roundTripBtn");
            const testBtn = document.getElementById("testBtn");

            // 初始化编辑器
            const vditorInstance = new Vditor("vditor", {
                mode: "wysiwyg",
                placeholder: "Hello, Vditor!",
                height: 300,
                input: (value) => {
                    // 调用 getHTML 方法获取 HTML
                    const html = vditorInstance.getHTML();
                    console.log("🔄 getHTML() 输出:\n", html);

                    // 将 HTML 直接显示在 HTML 输入框中
                    if (htmlInput) {
                        htmlInput.value = html;
                    }

                    // 自动将 HTML 转换为 Markdown 并显示在输出框中
                    const markdown = vditorInstance.html2md(html);

                    markdownOutput.value = markdown;
                },
                after: () => {
                    // 输出初始内容
                    setTimeout(() => {
                        const initialHtml = vditorInstance.getHTML();
                        console.log("🎯 初始 HTML 内容:", initialHtml);

                        // 将初始 HTML 显示在 HTML 输入框中
                        if (htmlInput) {
                            htmlInput.value = initialHtml;
                        }

                        // 自动转换初始 HTML 为 Markdown
                        try {
                            const initialMarkdown =
                                vditorInstance.html2md(initialHtml);
                            console.log(
                                "🎯 初始 Markdown 内容:",
                                initialMarkdown
                            );

                            if (markdownOutput) {
                                markdownOutput.value = initialMarkdown;
                            }
                        } catch (error) {
                            console.error("❌ 初始转换失败:", error);
                        }
                    }, 100);
                },
            });

            // 转换 HTML 到 Markdown
            async function convertHtml2Md() {
                const html = htmlInput.value.trim();

                // 使用编辑器实例的 html2md 方法
                const markdown = vditorInstance.html2md(html);

                markdownOutput.value = markdown;
            }
        </script>
    </body>
</html>
