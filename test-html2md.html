<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试 Vditor html2md 数学公式转换</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .input-html, .output-md {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
        }
        .output-md {
            background: #e8f5e8;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 3px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .error {
            color: red;
            background: #ffe6e6;
            padding: 10px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <h1>Vditor html2md 数学公式转换测试</h1>
    
    <div class="test-section">
        <h2>测试 1: 行内数学公式</h2>
        <div class="input-html" id="test1-input">
&lt;p&gt;这是一个行内数学公式：&lt;span class="language-math" data-math="a^2 + b^2 = c^2"&gt;渲染后的HTML内容&lt;/span&gt; 在文本中。&lt;/p&gt;
        </div>
        <button onclick="testHtml2Md('test1')">转换</button>
        <div class="output-md" id="test1-output">点击转换按钮查看结果</div>
    </div>

    <div class="test-section">
        <h2>测试 2: 块级数学公式</h2>
        <div class="input-html" id="test2-input">
&lt;div class="language-math" data-math="\frac{1}{\Bigl(\sqrt{\phi \sqrt{5}}-\phi\Bigr) e^{\frac25 \pi}} = 1+\frac{e^{-2\pi}} {1+\frac{e^{-4\pi}} {1+\frac{e^{-6\pi}}{1+\frac{e^{-8\pi}}{1+\cdots}}}}"&gt;复杂公式的渲染HTML&lt;/div&gt;
        </div>
        <button onclick="testHtml2Md('test2')">转换</button>
        <div class="output-md" id="test2-output">点击转换按钮查看结果</div>
    </div>

    <div class="test-section">
        <h2>测试 3: 混合内容（文本 + 数学公式）</h2>
        <div class="input-html" id="test3-input">
&lt;h2&gt;数学公式示例&lt;/h2&gt;
&lt;p&gt;勾股定理：&lt;span class="language-math" data-math="a^2 + b^2 = \color{red}c^2"&gt;HTML渲染&lt;/span&gt;&lt;/p&gt;
&lt;p&gt;欧拉公式：&lt;/p&gt;
&lt;div class="language-math" data-math="e^{i\pi} + 1 = 0"&gt;欧拉公式HTML渲染&lt;/div&gt;
&lt;p&gt;这是普通文本。&lt;/p&gt;
        </div>
        <button onclick="testHtml2Md('test3')">转换</button>
        <div class="output-md" id="test3-output">点击转换按钮查看结果</div>
    </div>

    <div class="test-section">
        <h2>测试 4: 无 data-math 属性的数学公式</h2>
        <div class="input-html" id="test4-input">
&lt;span class="language-math"&gt;x = \frac{-b \pm \sqrt{b^2-4ac}}{2a}&lt;/span&gt;
        </div>
        <button onclick="testHtml2Md('test4')">转换</button>
        <div class="output-md" id="test4-output">点击转换按钮查看结果</div>
    </div>

    <div class="test-section">
        <h2>测试 5: 自定义测试</h2>
        <textarea id="custom-input" rows="5" cols="80" placeholder="在这里输入要测试的HTML内容..."></textarea><br>
        <button onclick="testCustomHtml2Md()">转换自定义HTML</button>
        <div class="output-md" id="custom-output">输入HTML后点击转换</div>
    </div>

    <!-- 引入 Vditor -->
    <script src="https://unpkg.com/vditor/dist/index.min.js"></script>
    
    <script>
        // 预定义的测试用例HTML
        const testCases = {
            test1: '<p>这是一个行内数学公式：<span class="language-math" data-math="a^2 + b^2 = c^2">渲染后的HTML内容</span> 在文本中。</p>',
            test2: '<div class="language-math" data-math="\\frac{1}{\\Bigl(\\sqrt{\\phi \\sqrt{5}}-\\phi\\Bigr) e^{\\frac25 \\pi}} = 1+\\frac{e^{-2\\pi}} {1+\\frac{e^{-4\\pi}} {1+\\frac{e^{-6\\pi}}{1+\\frac{e^{-8\\pi}}{1+\\cdots}}}}">复杂公式的渲染HTML</div>',
            test3: `<h2>数学公式示例</h2>
<p>勾股定理：<span class="language-math" data-math="a^2 + b^2 = \\color{red}c^2">HTML渲染</span></p>
<p>欧拉公式：</p>
<div class="language-math" data-math="e^{i\\pi} + 1 = 0">欧拉公式HTML渲染</div>
<p>这是普通文本。</p>`,
            test4: '<span class="language-math">x = \\frac{-b \\pm \\sqrt{b^2-4ac}}{2a}</span>'
        };

        async function testHtml2Md(testId) {
            const outputElement = document.getElementById(testId + '-output');
            const html = testCases[testId];
            
            try {
                outputElement.textContent = '转换中...';
                
                // 调用 Vditor.html2md 方法
                const markdown = await Vditor.html2md(html, {
                    math: {
                        inlineDigit: false,
                        engine: 'KaTeX'
                    }
                });
                
                outputElement.textContent = markdown;
                outputElement.style.color = 'black';
            } catch (error) {
                outputElement.innerHTML = `<div class="error">转换失败: ${error.message}</div>`;
                console.error('转换错误:', error);
            }
        }

        async function testCustomHtml2Md() {
            const inputElement = document.getElementById('custom-input');
            const outputElement = document.getElementById('custom-output');
            const html = inputElement.value.trim();
            
            if (!html) {
                outputElement.innerHTML = '<div class="error">请输入HTML内容</div>';
                return;
            }
            
            try {
                outputElement.textContent = '转换中...';
                
                const markdown = await Vditor.html2md(html, {
                    math: {
                        inlineDigit: false,
                        engine: 'KaTeX'
                    }
                });
                
                outputElement.textContent = markdown;
                outputElement.style.color = 'black';
            } catch (error) {
                outputElement.innerHTML = `<div class="error">转换失败: ${error.message}</div>`;
                console.error('转换错误:', error);
            }
        }

        // 页面加载完成后的提示
        window.onload = function() {
            console.log('页面加载完成，可以开始测试 Vditor.html2md 函数');
            console.log('测试用例已准备就绪，点击各个转换按钮进行测试');
        };
    </script>
</body>
</html>
