{"name": "vditor", "version": "3.11.1", "description": "♏ 易于使用的 Markdown 编辑器，为适配不同的应用场景而生", "author": "<PERSON> <<EMAIL>> (http://vanessa.b3log.org)", "homepage": "https://b3log.org/vditor", "jsdelivr": "dist/index.min.js", "main": "dist/index.js", "funding": "https://ld246.com/sponsor", "files": ["dist/*", "src/index.ts", "src/method.ts", "src/ts/*", "src/assets/*"], "dependencies": {"diff-match-patch": "^1.0.5"}, "types": "dist/index.d.ts", "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@eslint/js": "9.12.0", "@types/diff-match-patch": "^1.0.32", "@types/jest": "^26.0.23", "@types/node": "^17.0.19", "@types/puppeteer": "^2.1.5", "@typescript-eslint/eslint-plugin": "^8.9.0", "@typescript-eslint/parser": "^8.9.0", "autoprefixer": "^10.4.2", "babel-loader": "^9.2.1", "clean-webpack-plugin": "^4.0.0-alpha.0", "copy-webpack-plugin": "^6.4.1", "css-loader": "^5.2.4", "eslint": "^9.12.0", "eslint-plugin-jest": "^26.1.1", "eslint-plugin-prettier": "5.2.1", "file-loader": "^6.2.0", "html-webpack-plugin": "^5.3.1", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "less": "^4.2.0", "less-loader": "^10.2.0", "mini-css-extract-plugin": "^2.6.0", "postcss": "^8.4.12", "postcss-loader": "^5.2.0", "prettier": "3.4.1", "puppeteer": "^23.0.2", "style-loader": "^1.3.0", "terser-webpack-plugin": "^5.3.0", "ts-jest": "^26.5.6", "ts-loader": "^9.5.1", "typescript": "^4.9.5", "typescript-eslint": "^8.9.0", "webpack": "^5.66.0", "webpack-bundle-analyzer": "^4.10.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.1.0"}, "license": "MIT", "repository": "git://github.com/Vanessa219/vditor.git", "bugs": {"url": "https://github.com/Vanessa219/vditor/issues"}, "scripts": {"lint": "eslint eslint.config.mjs", "test:watch": "jest --watch", "test": "jest --coverage", "start": "webpack serve --config webpack.start.js", "build": "webpack"}, "keywords": ["editor", "markdown", "b3log"]}